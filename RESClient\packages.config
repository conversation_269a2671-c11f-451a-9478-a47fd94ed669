﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="7z.Libs" version="25.00" targetFramework="net48" />
  <package id="BouncyCastle.Cryptography" version="2.3.1" targetFramework="net48" />
  <package id="Enums.NET" version="4.0.1" targetFramework="net48" />
  <package id="ExtendedNumerics.BigDecimal" version="2025.1001.2.129" targetFramework="net48" />
  <package id="MathNet.Numerics.Signed" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.IO.RecyclableMemoryStream" version="3.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="NPOI" version="2.7.3" targetFramework="net48" />
  <package id="SharpZipLib" version="1.4.2" targetFramework="net48" />
  <package id="SixLabors.Fonts" version="1.0.1" targetFramework="net48" />
  <package id="SixLabors.ImageSharp" version="3.1.11" targetFramework="net48" />
  <package id="Squid-Box.SevenZipSharp.Lite" version="********" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.7" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Pkcs" version="8.0.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.Xml" version="8.0.2" targetFramework="net48" />
  <package id="System.Text.Encoding.CodePages" version="5.0.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.7" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.7" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>